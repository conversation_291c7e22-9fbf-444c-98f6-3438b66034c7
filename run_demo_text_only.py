# -*- coding: utf-8 -*-
"""
运行柔性作业车间调度演示 - 纯文本版本

功能：
1. 演示简化的3工件3机器FJSP实例
2. 展示FIFO调度规则的详细决策过程
3. 解决中文显示问题，不依赖图形界面
4. 提供调度规则的理论解释

适用场景：
- 学习调度规则基本概念
- 理解FJSP问题结构
- 验证调度算法正确性

作者：AI Assistant
日期：2025年
"""

import sys
import os

# 设置控制台编码为UTF-8，解决Windows中文显示问题
if sys.platform.startswith('win'):
    os.system('chcp 65001')

class FJSPTextDemo:
    def __init__(self):
        """
        柔性作业车间调度问题实例演示 - 纯文本版
        """
        self.num_machines = 3
        
        # 定义实例数据：3工件，每工件2-3道工序，3台机器
        self.jobs = [
            # 工件1：3道工序
            [
                [(0, 3), (1, 2), (2, 4)],  # 工序1：机器1用时3，机器2用时2，机器3用时4
                [(0, 2), (2, 3)],          # 工序2：机器1用时2，机器3用时3
                [(1, 4), (2, 2)]           # 工序3：机器2用时4，机器3用时2
            ],
            # 工件2：2道工序
            [
                [(0, 4), (1, 3)],          # 工序1：机器1用时4，机器2用时3
                [(1, 2), (2, 5)]           # 工序2：机器2用时2，机器3用时5
            ],
            # 工件3：3道工序
            [
                [(1, 2), (2, 3)],          # 工序1：机器2用时2，机器3用时3
                [(0, 3), (1, 4)],          # 工序2：机器1用时3，机器2用时4
                [(0, 2), (2, 4)]           # 工序3：机器1用时2，机器3用时4
            ]
        ]
        
        print("=== 实例数据说明 ===")
        print("工件数：3，机器数：3")
        for i, job in enumerate(self.jobs):
            print(f"工件{i+1}：{len(job)}道工序")
            for j, op in enumerate(job):
                machines_info = [f"机器{m+1}({t}min)" for m, t in op]
                print(f"  工序{j+1}：{', '.join(machines_info)}")
        print()

    def fifo_demo(self):
        """FIFO调度规则演示"""
        print("=== FIFO (先到先服务) 调度演示 ===")
        print("规则：优先调度最早准备就绪的操作")
        
        machine_available = [0, 0, 0]
        job_next_op = [0, 0, 0]
        job_last_completion = [0, 0, 0]
        schedule = []
        step = 1
        
        while sum(job_next_op[i] < len(self.jobs[i]) for i in range(3)) > 0:
            print(f"\n--- 调度步骤 {step} ---")
            
            # 找到准备就绪的操作
            ready_ops = []
            for job_id in range(3):
                if job_next_op[job_id] < len(self.jobs[job_id]):
                    ready_time = job_last_completion[job_id]
                    ready_ops.append((ready_time, job_id, job_next_op[job_id]))
            
            print("当前可调度操作：")
            for ready_time, job_id, op_id in ready_ops:
                print(f"  工件{job_id+1}-工序{op_id+1}，就绪时间：{ready_time}")
            
            # FIFO选择：最早就绪的操作
            ready_ops.sort()
            selected_ready_time, selected_job, selected_op = ready_ops[0]
            
            print(f"FIFO选择：工件{selected_job+1}-工序{selected_op+1}")
            
            # 机器分配
            op_machines = self.jobs[selected_job][selected_op]
            best_machine, best_start, best_duration = None, float('inf'), 0
            
            for machine_id, duration in op_machines:
                start_time = max(machine_available[machine_id], selected_ready_time)
                # 选择最早开始时间的机器，如果开始时间相同则选择加工时间最短的
                if start_time < best_start or (start_time == best_start and duration < best_duration):
                    best_start, best_machine, best_duration = start_time, machine_id, duration
            
            completion_time = best_start + best_duration
            print(f"分配到机器{best_machine+1}：开始{best_start}，结束{completion_time}")
            
            # 更新状态
            machine_available[best_machine] = completion_time
            job_last_completion[selected_job] = completion_time
            job_next_op[selected_job] += 1
            
            schedule.append({
                'job': selected_job, 'operation': selected_op, 'machine': best_machine,
                'start': best_start, 'duration': best_duration, 'end': completion_time
            })
            
            step += 1
        
        makespan = max(machine_available)
        print(f"\nFIFO总完工时间：{makespan}")
        return schedule, makespan

    def print_schedule_table(self, schedules_dict):
        """打印调度结果对比表"""
        print("\n=== 调度结果对比表 ===")
        print("规则\t总完工时间\t机器利用率")
        print("-" * 40)
        
        for rule, (schedule, makespan) in schedules_dict.items():
            # 计算机器利用率
            machine_work_time = [0] * self.num_machines
            for item in schedule:
                machine_work_time[item['machine']] += item['duration']
            
            total_work_time = sum(machine_work_time)
            utilization = total_work_time / (self.num_machines * makespan)
            
            print(f"{rule}\t{makespan}\t\t{utilization:.1%}")
        
        print("\n=== 详细调度序列 ===")
        for rule, (schedule, makespan) in schedules_dict.items():
            print(f"\n{rule}调度序列：")
            for i, item in enumerate(schedule, 1):
                print(f"  {i}. 工件{item['job']+1}-工序{item['operation']+1} "
                      f"在机器{item['machine']+1}：{item['start']}-{item['end']} "
                      f"(时长{item['duration']})")

    def run_simple_demo(self):
        """运行简化演示"""
        print("柔性作业车间调度问题四种调度规则演示")
        print("=" * 50)
        
        # 只运行FIFO作为示例
        schedules = {}
        schedules['FIFO'] = self.fifo_demo()
        
        # 可以在这里添加其他规则的简化版本
        print("\n" + "=" * 50)
        print("=== 关键调度决策点分析 ===")
        print("\n1. 初始阶段 (t=0):")
        print("   所有工件的第一道工序都准备就绪")
        print("   • FIFO: 按工件编号顺序选择工件1")
        print("   • MOPNR: 选择剩余操作数最多的工件")
        print("   • SPT: 选择最短加工时间的操作-机器组合")
        print("   • MWKR: 选择剩余工作量最大的工件")
        
        print("\n2. 机器选择策略:")
        print("   • FIFO/MOPNR/MWKR: 先选操作，再选最早可用机器")
        print("   • SPT: 同时考虑操作和机器，选择全局最短时间")
        
        print("\n3. 适用场景建议:")
        print("   • FIFO: 公平性要求高，简单易实现的场景")
        print("   • MOPNR: 工件操作数差异大，需要平衡进度的场景")
        print("   • SPT: 优先完成短任务，提高系统响应速度的场景")
        print("   • MWKR: 工件加工时间差异大，需要避免重工件积压的场景")
        
        return schedules

if __name__ == "__main__":
    demo = FJSPTextDemo()
    results = demo.run_simple_demo()
