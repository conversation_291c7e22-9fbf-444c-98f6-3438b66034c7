"""
柔性作业车间调度问题(FJSP)四种调度规则实现

本文件实现了四种经典的FJSP调度规则：
1. FIFO - 先到先服务
2. MOPNR - 剩余操作数最多优先
3. SPT - 最短加工时间优先
4. MWKR - 剩余工作量最多优先

使用MK01标准基准实例进行测试验证。

作者：AI Assistant
日期：2025年
"""

import random
import copy
from typing import List, Tuple, Dict

class FJSPScheduler:
    """
    柔性作业车间调度器

    实现四种经典调度规则，并提供完整的验证和性能分析功能。
    """

    def __init__(self):
        """
        初始化调度器

        MK01实例配置：
        - 10个工件
        - 6台机器
        - 每个工件有5-6道工序
        - 每道工序可在多台机器上加工（柔性特征）
        """
        self.num_machines = 6  # MK01实例有6台机器
        
    def parse_mk01_data(self):
        """
        解析MK01标准数据格式

        MK01数据格式说明：
        每行代表一个工件，格式为：
        [操作数] [机器数1] [机器ID1] [时间1] [机器ID2] [时间2] ... [机器数2] [机器ID3] [时间3] ...

        例如："6 2 1 5 3 4 ..." 表示：
        - 工件有6道工序
        - 第1道工序可在2台机器上加工：机器1用时5，机器3用时4
        - 后续类似...

        返回:
            list: jobs[i][j] = [(machine_id, processing_time), ...]
                 表示工件i的第j个操作可在哪些机器上加工及对应时间
        """
        # MK01标准实例原始数据（10个工件）
        raw_lines = [
            # 工件1：6道工序
            "6 2 1 5 3 4 3 5 3 3 5 2 1 2 3 4 6 2 3 6 5 2 6 1 1 1 3 1 3 6 6 3 6 4 3",
            # 工件2：5道工序
            "5 1 2 6 1 3 1 1 1 2 2 2 6 4 6 3 6 5 2 6 1 1",
            # 工件3：5道工序
            "5 1 2 6 2 3 4 6 2 3 6 5 2 6 1 1 3 3 4 2 6 6 6 2 1 1 5 5",
            # 工件4：5道工序
            "5 3 6 5 2 6 1 1 1 2 6 1 3 1 3 5 3 3 5 2 1 2 3 4 6 2",
            # 工件5：6道工序
            "6 3 5 3 3 5 2 1 3 6 5 2 6 1 1 1 2 6 2 1 5 3 4 2 2 6 4 6 3 3 4 2 6 6 6",
            # 工件6：6道工序
            "6 2 3 4 6 2 1 1 2 3 3 4 2 6 6 6 1 2 6 3 6 5 2 6 1 1 2 1 3 4 2",
            # 工件7：5道工序
            "5 1 6 1 2 1 3 4 2 3 3 4 2 6 6 6 3 2 6 5 1 1 6 1 3 1",
            # 工件8：5道工序
            "5 2 3 4 6 2 3 3 4 2 6 6 6 3 6 5 2 6 1 1 1 2 6 2 2 6 4 6",
            # 工件9：6道工序
            "6 1 6 1 2 1 1 5 5 3 6 6 3 6 4 3 1 1 2 3 3 4 2 6 6 6 2 2 6 4 6",
            # 工件10：6道工序
            "6 2 3 4 6 2 3 3 4 2 6 6 6 3 5 3 3 5 2 1 1 6 1 2 2 6 4 6 2 1 3 4 2"
        ]
        
        # 解析数据结构
        jobs = []
        for job_idx, line in enumerate(raw_lines):
            numbers = list(map(int, line.split()))  # 将字符串分割并转换为整数，list
            num_operations = numbers[0]  # 该工件的操作数
            operations = []

            idx = 1  # 数据索引指针
            # 解析每道工序
            for op in range(num_operations):
                num_machines = numbers[idx]  # 该工序可用的机器数
                idx += 1
                machine_options = []

                # 解析每台可用机器及其加工时间
                for m in range(num_machines):
                    machine_id = numbers[idx] - 1  # 转换为0-based索引（原数据是1-based）
                    processing_time = numbers[idx + 1]  # 在该机器上的加工时间
                    machine_options.append((machine_id, processing_time))
                    idx += 2  # 跳过机器ID和加工时间

                operations.append(machine_options)
            jobs.append(operations)

        return jobs
    
    def calculate_remaining_work(self, job_id, current_op, jobs):
        """计算剩余工作量（MWKR用）- 包括当前操作和后续操作的平均加工时间"""
        remaining_time = 0
        remaining_ops = 0

        # 包括当前操作和所有后续操作
        for op_idx in range(current_op, len(jobs[job_id])):
            # 计算该工序的平均加工时间
            op_times = [time for _, time in jobs[job_id][op_idx]]
            avg_time = sum(op_times) / len(op_times) if op_times else 0
            remaining_time += avg_time
            remaining_ops += 1

        return remaining_time, remaining_ops
    
    def fifo_scheduling(self, jobs):
        """
        FIFO (First In First Out) 调度规则 - 先到先服务

        算法原理:
            FIFO规则遵循"先到先服务"的原则，优先调度最早准备就绪的操作。
            对于每个调度决策点，选择就绪时间最早的操作，然后为其分配最早可用的兼容机器。

        参数:
            jobs (list): 工件数据，jobs[i][j] = [(machine_id, processing_time), ...]
                        表示工件i的第j个操作可以在哪些机器上加工及对应的加工时间

        返回:
            tuple: (makespan, schedule)
                makespan (int): 总完工时间（所有工件完成的最晚时间）
                schedule (list): 详细调度方案，每个元素包含工件、操作、机器、时间等信息

        算法步骤:
            1. 初始化：设置机器可用时间、工件进度跟踪等数据结构
            2. 循环调度：直到所有操作都被调度完成
               a) 识别所有准备就绪的操作（前序操作已完成）
               b) 按就绪时间排序，选择最早就绪的操作
               c) 为选中操作分配最早可用的兼容机器
               d) 更新机器可用时间和工件进度
            3. 计算并返回makespan和完整调度方案
        """
        # === 1. 初始化数据结构 ===
        num_jobs = len(jobs)  # 工件总数

        # 机器可用时间数组：machine_available_time[i] 表示机器i的最早可用时间
        machine_available_time = [0] * self.num_machines

        # 工件进度跟踪：job_next_operation[i] 表示工件i下一个要处理的操作索引
        job_next_operation = [0] * num_jobs

        # 工件完成时间：job_last_completion_time[i] 表示工件i上一个操作的完成时间
        job_last_completion_time = [0] * num_jobs

        # 调度结果存储
        schedule = []
        total_operations = sum(len(job) for job in jobs)  # 总操作数
        completed_operations = 0  # 已完成操作数

        print("=== FIFO调度过程 ===")

        # === 2. 主调度循环 ===
        while completed_operations < total_operations:
            # === 2a. 识别所有准备就绪的操作 ===
            ready_operations = []  # 存储(就绪时间, 工件ID, 操作ID)的元组

            for job_id in range(num_jobs):
                # 检查工件是否还有未完成的操作
                if job_next_operation[job_id] < len(jobs[job_id]):
                    # 该工件的下一个操作已准备就绪（前序操作已完成）
                    ready_time = job_last_completion_time[job_id]  # 该操作的最早开始时间
                    ready_operations.append((ready_time, job_id, job_next_operation[job_id]))

            # 如果没有准备就绪的操作，退出循环（理论上不应该发生）
            if not ready_operations:
                break

            # === 2b. 按FIFO规则选择操作 ===
            # 排序规则：按就绪时间升序，时间相同时按工件ID和操作ID升序（保证稳定性）
            ready_operations.sort(key=lambda x: (x[0], x[1], x[2]))
            earliest_ready_time, selected_job, selected_op = ready_operations[0]

            # === 2c. 为选中操作分配机器 ===
            # 获取该操作的所有兼容机器及其加工时间
            operation_machines = jobs[selected_job][selected_op]

            # 在所有兼容机器中选择最早可用的机器
            best_machine = None          # 最佳机器ID
            best_start_time = float('inf')  # 最早开始时间
            best_processing_time = 0     # 对应的加工时间

            for machine_id, processing_time in operation_machines:
                # 计算在该机器上的实际开始时间
                # 必须同时满足：机器可用 且 工件前序操作已完成
                start_time = max(machine_available_time[machine_id], earliest_ready_time)

                # 选择开始时间最早的机器（FIFO的机器选择策略）
                if start_time < best_start_time:
                    best_start_time = start_time
                    best_machine = machine_id
                    best_processing_time = processing_time

            # === 2d. 执行调度并更新状态 ===
            completion_time = best_start_time + best_processing_time

            # 更新机器可用时间
            machine_available_time[best_machine] = completion_time

            # 更新工件状态
            job_last_completion_time[selected_job] = completion_time  # 更新工件完成时间
            job_next_operation[selected_job] += 1  # 工件进入下一个操作

            # 记录调度结果（转换为1-based索引用于显示）
            schedule.append({
                'job': selected_job + 1,           # 工件编号（1-based）
                'operation': selected_op + 1,      # 操作编号（1-based）
                'machine': best_machine + 1,       # 机器编号（1-based）
                'start_time': best_start_time,     # 开始时间
                'processing_time': best_processing_time,  # 加工时间
                'completion_time': completion_time  # 完成时间
            })

            completed_operations += 1

            # 显示前10个操作的调度详情（用于调试和验证）
            if completed_operations <= 10:
                print(f"工件{selected_job+1} 工序{selected_op+1}: 机器{best_machine+1}, "
                      f"开始{best_start_time}, 时长{best_processing_time}, 完成{completion_time}")

        # === 3. 计算最终结果 ===
        makespan = max(machine_available_time)  # 所有机器中最晚的完成时间
        print(f"FIFO总排产时间: {makespan}\n")
        return makespan, schedule
    
    def mopnr_scheduling(self, jobs):
        """
        MOPNR (Most Operations Remaining) 调度规则 - 剩余操作数最多优先

        算法原理:
            MOPNR规则优先调度剩余操作数最多的工件，目的是平衡各工件的进度，
            避免某些工件过早完成而其他工件滞后，从而减少整体的完工时间。
            这是一种基于工件负载均衡的启发式调度策略。

        参数:
            jobs (list): 工件数据结构，格式同FIFO规则

        返回:
            tuple: (makespan, schedule) 格式同FIFO规则

        算法特点:
            - 优先级判断：剩余操作数越多，优先级越高
            - 负载均衡：避免工件间进度差异过大
            - 适用场景：工件操作数差异较大的情况

        计算公式:
            剩余操作数 = 工件总操作数 - 当前操作索引 - 1
            (不包括当前正在考虑的操作，只计算后续操作)
        """
        # === 1. 初始化数据结构 ===
        num_jobs = len(jobs)
        machine_available_time = [0] * self.num_machines  # 机器可用时间
        job_next_operation = [0] * num_jobs               # 工件下一操作索引
        job_last_completion_time = [0] * num_jobs         # 工件上次完成时间

        schedule = []
        total_operations = sum(len(job) for job in jobs)
        completed_operations = 0

        print("=== MOPNR调度过程 ===")

        # === 2. 主调度循环 ===
        while completed_operations < total_operations:
            # === 2a. 识别准备就绪的操作并计算剩余操作数 ===
            ready_operations = []  # 存储(剩余操作数, 就绪时间, 工件ID, 操作ID)

            for job_id in range(num_jobs):
                if job_next_operation[job_id] < len(jobs[job_id]):
                    ready_time = job_last_completion_time[job_id]

                    # 计算剩余操作数：当前操作完成后还剩多少个操作
                    # 例如：工件有6个操作，当前处理第1个操作(索引0)，则剩余操作数 = 6-0-1 = 5
                    remaining_ops = len(jobs[job_id]) - job_next_operation[job_id] - 1

                    ready_operations.append((remaining_ops, ready_time, job_id, job_next_operation[job_id]))

            if not ready_operations:
                break

            # === 2b. 按MOPNR规则选择操作 ===
            # 排序规则：
            # 1. 剩余操作数降序（-x[0]）：剩余操作多的优先
            # 2. 就绪时间升序（x[1]）：时间早的优先
            # 3. 工件ID升序（x[2]）：编号小的优先（稳定性）
            # 4. 操作ID升序（x[3]）：操作编号小的优先（稳定性）
            ready_operations.sort(key=lambda x: (-x[0], x[1], x[2], x[3]))
            _, earliest_ready_time, selected_job, selected_op = ready_operations[0]

            # === 2c. 为选中操作分配最早可用的兼容机器 ===
            operation_machines = jobs[selected_job][selected_op]

            best_machine = None
            best_start_time = float('inf')
            best_processing_time = 0

            # 遍历所有兼容机器，选择最早可用的
            for machine_id, processing_time in operation_machines:
                start_time = max(machine_available_time[machine_id], earliest_ready_time)
                if start_time < best_start_time:
                    best_start_time = start_time
                    best_machine = machine_id
                    best_processing_time = processing_time

            # === 2d. 执行调度并更新状态 ===
            completion_time = best_start_time + best_processing_time
            machine_available_time[best_machine] = completion_time
            job_last_completion_time[selected_job] = completion_time
            job_next_operation[selected_job] += 1

            # 记录调度结果
            schedule.append({
                'job': selected_job + 1,
                'operation': selected_op + 1,
                'machine': best_machine + 1,
                'start_time': best_start_time,
                'processing_time': best_processing_time,
                'completion_time': completion_time
            })

            completed_operations += 1

            # 显示调度详情（包含剩余操作数信息）
            if completed_operations <= 10:
                remaining_after = len(jobs[selected_job]) - job_next_operation[selected_job]
                print(f"工件{selected_job+1} 工序{selected_op+1} (剩余{remaining_after}): 机器{best_machine+1}, "
                      f"开始{best_start_time}, 时长{best_processing_time}, 完成{completion_time}")

        makespan = max(machine_available_time)
        print(f"MOPNR总排产时间: {makespan}\n")
        return makespan, schedule
    
    def spt_scheduling(self, jobs):
        """SPT调度规则 - 最短加工时间优先"""
        num_jobs = len(jobs)
        machine_available_time = [0] * self.num_machines
        job_next_operation = [0] * num_jobs
        job_last_completion_time = [0] * num_jobs
        
        schedule = []
        total_operations = sum(len(job) for job in jobs)
        completed_operations = 0
        
        print("=== SPT调度过程 ===")
        
        while completed_operations < total_operations:
            # 找到所有准备就绪的操作-机器对
            ready_pairs = []
            for job_id in range(num_jobs):
                if job_next_operation[job_id] < len(jobs[job_id]):
                    operation_machines = jobs[job_id][job_next_operation[job_id]]
                    ready_time = job_last_completion_time[job_id]
                    
                    for machine_id, processing_time in operation_machines:
                        start_time = max(machine_available_time[machine_id], ready_time)
                        ready_pairs.append((processing_time, start_time, job_id, job_next_operation[job_id], machine_id))
            
            if not ready_pairs:
                break
            
            # 按加工时间升序排序，选择最短加工时间的操作-机器对
            ready_pairs.sort(key=lambda x: (x[0], x[1], x[2], x[3], x[4]))
            best_processing_time, best_start_time, selected_job, selected_op, best_machine = ready_pairs[0]
            
            # 调度该操作
            completion_time = best_start_time + best_processing_time
            machine_available_time[best_machine] = completion_time
            job_last_completion_time[selected_job] = completion_time
            job_next_operation[selected_job] += 1
            
            schedule.append({
                'job': selected_job + 1,
                'operation': selected_op + 1,
                'machine': best_machine + 1,
                'start_time': best_start_time,
                'processing_time': best_processing_time,
                'completion_time': completion_time
            })
            
            completed_operations += 1
            
            if completed_operations <= 10:
                print(f"工件{selected_job+1} 工序{selected_op+1} (时长{best_processing_time}): 机器{best_machine+1}, "
                      f"开始{best_start_time}, 完成{completion_time}")
        
        makespan = max(machine_available_time)
        print(f"SPT总排产时间: {makespan}\n")
        return makespan, schedule
    
    def mwkr_scheduling(self, jobs):
        """MWKR调度规则 - 剩余工作量最多优先"""
        num_jobs = len(jobs)
        machine_available_time = [0] * self.num_machines
        job_next_operation = [0] * num_jobs
        job_last_completion_time = [0] * num_jobs
        
        schedule = []
        total_operations = sum(len(job) for job in jobs)
        completed_operations = 0
        
        print("=== MWKR调度过程 ===")
        
        while completed_operations < total_operations:
            # 找到所有准备就绪的操作
            ready_operations = []
            for job_id in range(num_jobs):
                if job_next_operation[job_id] < len(jobs[job_id]):
                    ready_time = job_last_completion_time[job_id]
                    remaining_work, _ = self.calculate_remaining_work(job_id, job_next_operation[job_id], jobs)
                    ready_operations.append((remaining_work, ready_time, job_id, job_next_operation[job_id]))
            
            if not ready_operations:
                break
            
            # 按剩余工作量降序排序，剩余工作量大的优先
            ready_operations.sort(key=lambda x: (-x[0], x[1], x[2], x[3]))
            _, earliest_ready_time, selected_job, selected_op = ready_operations[0]
            
            # 获取该操作的机器选项
            operation_machines = jobs[selected_job][selected_op]
            
            # 选择最早可用的兼容机器
            best_machine = None
            best_start_time = float('inf')
            best_processing_time = 0
            
            for machine_id, processing_time in operation_machines:
                start_time = max(machine_available_time[machine_id], earliest_ready_time)
                if start_time < best_start_time:
                    best_start_time = start_time
                    best_machine = machine_id
                    best_processing_time = processing_time
            
            # 调度该操作
            completion_time = best_start_time + best_processing_time
            machine_available_time[best_machine] = completion_time
            job_last_completion_time[selected_job] = completion_time
            job_next_operation[selected_job] += 1
            
            schedule.append({
                'job': selected_job + 1,
                'operation': selected_op + 1,
                'machine': best_machine + 1,
                'start_time': best_start_time,
                'processing_time': best_processing_time,
                'completion_time': completion_time
            })
            
            completed_operations += 1
            
            if completed_operations <= 10:
                remaining_work_after, _ = self.calculate_remaining_work(selected_job, selected_op, jobs)
                print(f"工件{selected_job+1} 工序{selected_op+1} (剩余工作{remaining_work_after:.1f}): 机器{best_machine+1}, "
                      f"开始{best_start_time}, 时长{best_processing_time}, 完成{completion_time}")
        
        makespan = max(machine_available_time)
        print(f"MWKR总排产时间: {makespan}\n")
        return makespan, schedule
    
    def validate_schedule(self, schedule, jobs):
        """
        验证调度结果的正确性

        参数:
            schedule: 调度结果列表，每个元素包含工件、操作、机器、开始时间、完成时间等信息
            jobs: 原始工件数据

        返回:
            dict: 包含各种验证结果的字典
        """
        validation_results = {
            'precedence_valid': True,      # 工序优先级约束是否满足
            'machine_conflict': False,     # 是否存在机器冲突
            'processing_time_valid': True, # 加工时间是否正确
            'makespan_correct': True,      # makespan计算是否正确
            'errors': []                   # 错误详情列表
        }

        # 1. 检查工序优先级约束
        job_operations = {}  # 记录每个工件的操作完成时间
        for item in schedule:
            job_id = item['job'] - 1  # 转换为0-based索引
            op_id = item['operation'] - 1
            completion_time = item['completion_time']

            if job_id not in job_operations:
                job_operations[job_id] = {}
            job_operations[job_id][op_id] = completion_time

            # 检查前序操作是否已完成
            if op_id > 0:
                if op_id - 1 not in job_operations[job_id]:
                    validation_results['precedence_valid'] = False
                    validation_results['errors'].append(
                        f"工件{job_id+1}操作{op_id+1}的前序操作{op_id}未完成"
                    )
                elif job_operations[job_id][op_id - 1] > item['start_time']:
                    validation_results['precedence_valid'] = False
                    validation_results['errors'].append(
                        f"工件{job_id+1}操作{op_id+1}开始时间{item['start_time']}"
                        f"早于前序操作完成时间{job_operations[job_id][op_id - 1]}"
                    )

        # 2. 检查机器冲突
        machine_schedules = {}  # 记录每台机器的调度情况
        for item in schedule:
            machine_id = item['machine']
            start_time = item['start_time']
            completion_time = item['completion_time']

            if machine_id not in machine_schedules:
                machine_schedules[machine_id] = []
            machine_schedules[machine_id].append((start_time, completion_time, item))

        # 检查每台机器是否存在时间冲突
        for machine_id, operations in machine_schedules.items():
            operations.sort(key=lambda x: x[0])  # 按开始时间排序
            for i in range(len(operations) - 1):
                current_end = operations[i][1]
                next_start = operations[i + 1][0]
                if current_end > next_start:
                    validation_results['machine_conflict'] = True
                    validation_results['errors'].append(
                        f"机器{machine_id}存在时间冲突: "
                        f"操作{operations[i][2]['job']}-{operations[i][2]['operation']}结束时间{current_end} > "
                        f"操作{operations[i+1][2]['job']}-{operations[i+1][2]['operation']}开始时间{next_start}"
                    )

        # 3. 检查加工时间正确性
        for item in schedule:
            job_id = item['job'] - 1
            op_id = item['operation'] - 1
            machine_id = item['machine'] - 1
            expected_time = item['processing_time']
            actual_duration = item['completion_time'] - item['start_time']

            # 查找该操作在该机器上的标准加工时间
            operation_machines = jobs[job_id][op_id]
            standard_time = None
            for m_id, p_time in operation_machines:
                if m_id == machine_id:
                    standard_time = p_time
                    break

            if standard_time is None:
                validation_results['processing_time_valid'] = False
                validation_results['errors'].append(
                    f"工件{job_id+1}操作{op_id+1}不能在机器{machine_id+1}上加工"
                )
            elif standard_time != expected_time or actual_duration != expected_time:
                validation_results['processing_time_valid'] = False
                validation_results['errors'].append(
                    f"工件{job_id+1}操作{op_id+1}在机器{machine_id+1}上的加工时间不正确: "
                    f"标准{standard_time}, 记录{expected_time}, 实际{actual_duration}"
                )

        return validation_results

    def calculate_performance_metrics(self, schedule, jobs):
        """
        计算调度性能指标

        参数:
            schedule: 调度结果
            jobs: 原始工件数据

        返回:
            dict: 包含各种性能指标的字典
        """
        metrics = {}

        # 1. Makespan (最大完成时间)
        metrics['makespan'] = max(item['completion_time'] for item in schedule)

        # 2. 机器利用率
        machine_work_time = {}
        for item in schedule:
            machine_id = item['machine']
            processing_time = item['processing_time']
            if machine_id not in machine_work_time:
                machine_work_time[machine_id] = 0
            machine_work_time[machine_id] += processing_time

        total_work_time = sum(machine_work_time.values())
        metrics['total_work_time'] = total_work_time
        metrics['average_utilization'] = total_work_time / (self.num_machines * metrics['makespan'])
        metrics['machine_utilization'] = {
            machine_id: work_time / metrics['makespan']
            for machine_id, work_time in machine_work_time.items()
        }

        # 3. 工件完成时间
        job_completion_times = {}
        for item in schedule:
            job_id = item['job']
            if job_id not in job_completion_times:
                job_completion_times[job_id] = 0
            job_completion_times[job_id] = max(job_completion_times[job_id], item['completion_time'])

        metrics['job_completion_times'] = job_completion_times
        metrics['average_completion_time'] = sum(job_completion_times.values()) / len(job_completion_times)

        # 4. 调度效率指标
        total_operations = sum(len(job) for job in jobs)
        metrics['total_operations'] = total_operations
        metrics['operations_per_time_unit'] = total_operations / metrics['makespan']

        return metrics

    def run_all_rules(self):
        """运行所有四种调度规则并进行验证"""
        jobs = self.parse_mk01_data()

        print("=== MK01 数据验证 ===")
        print(f"工件数量: {len(jobs)}")
        for i, job in enumerate(jobs):
            print(f"工件{i+1}: {len(job)}道工序")
        print()

        rules = [
            ("FIFO", self.fifo_scheduling),
            ("MOPNR", self.mopnr_scheduling),
            ("SPT", self.spt_scheduling),
            ("MWKR", self.mwkr_scheduling)
        ]

        results = {}
        detailed_results = {}

        for rule_name, rule_func in rules:
            makespan, schedule = rule_func(jobs)
            results[rule_name] = makespan

            # 验证调度结果
            validation = self.validate_schedule(schedule, jobs)
            metrics = self.calculate_performance_metrics(schedule, jobs)

            detailed_results[rule_name] = {
                'makespan': makespan,
                'schedule': schedule,
                'validation': validation,
                'metrics': metrics
            }

        print("=== 最终结果汇总 ===")
        for rule_name, makespan in results.items():
            validation = detailed_results[rule_name]['validation']
            status = "✓" if all([
                validation['precedence_valid'],
                not validation['machine_conflict'],
                validation['processing_time_valid']
            ]) else "✗"
            print(f"{rule_name}调度规则: {makespan} {status}")

        if results:
            best_rule = min(results.items(), key=lambda x: x[1])
            print(f"\n本次测试最佳规则: {best_rule[0]} (Makespan: {best_rule[1]})")

        print(f"\n参考信息: MK01已知最优解范围为36-42")
        return results, detailed_results

# 执行计算
scheduler = FJSPScheduler()
results = scheduler.run_all_rules()