"""
交互式FJSP学习导师
帮助您深入理解柔性作业车间调度问题的每个算法细节
"""

import json
from test import FJSPScheduler

class FJSPTutor:
    def __init__(self):
        self.scheduler = FJSPScheduler()
        self.jobs = self.scheduler.parse_mk01_data()
        
    def explain_data_structure(self):
        """详细解释数据结构"""
        print("📊 FJSP数据结构详解")
        print("=" * 50)
        
        print("原始数据格式（以工件1为例）:")
        raw_line = "6 2 1 5 3 4 3 5 3 3 5 2 1 2 3 4 6 2 3 6 5 2 6 1 1 1 3 1 3 6 6 3 6 4 3"
        print(f"原始: {raw_line}")
        
        print("\n解析过程:")
        numbers = list(map(int, raw_line.split()))
        print(f"数字数组: {numbers[:10]}... (共{len(numbers)}个数字)")
        
        print(f"\n第一个数字 {numbers[0]} = 工件1有6道工序")
        
        idx = 1
        for op in range(3):  # 只显示前3道工序
            num_machines = numbers[idx]
            print(f"\n工序{op+1}:")
            print(f"  可用机器数: {num_machines}")
            idx += 1
            
            machines = []
            for m in range(num_machines):
                machine_id = numbers[idx] - 1  # 转为0-based
                processing_time = numbers[idx + 1]
                machines.append((machine_id, processing_time))
                print(f"    机器{machine_id+1}: 加工时间{processing_time}")
                idx += 2
            
        print(f"\n解析后的数据结构:")
        print(f"jobs[0][:3] = {self.jobs[0][:3]}")
        
    def compare_scheduling_rules(self):
        """对比不同调度规则的决策逻辑"""
        print("\n🎯 调度规则对比分析")
        print("=" * 50)
        
        # 创建一个简单的调度场景
        print("假设场景：时间点5，有以下操作准备就绪")
        ready_ops = [
            {"job": 1, "op": 2, "ready_time": 5, "remaining_ops": 3, "remaining_work": 15.5, "machines": [(0, 3), (1, 2)]},
            {"job": 2, "op": 1, "ready_time": 5, "remaining_ops": 4, "remaining_work": 18.2, "machines": [(0, 4), (2, 1)]},
            {"job": 3, "op": 3, "ready_time": 5, "remaining_ops": 1, "remaining_work": 6.0, "machines": [(1, 5), (2, 3)]}
        ]
        
        for op in ready_ops:
            print(f"工件{op['job']}工序{op['op']}: 剩余{op['remaining_ops']}个操作, 剩余工作量{op['remaining_work']}")
            print(f"  机器选项: {[(f'机器{m+1}', f'时长{t}') for m, t in op['machines']]}")
        
        print(f"\n各规则的选择逻辑:")
        
        # FIFO: 按就绪时间
        print("FIFO: 按就绪时间排序 -> 都是时间5，按工件编号 -> 选择工件1")
        
        # MOPNR: 按剩余操作数
        sorted_by_ops = sorted(ready_ops, key=lambda x: -x['remaining_ops'])
        print(f"MOPNR: 按剩余操作数排序 -> 工件{sorted_by_ops[0]['job']}(剩余{sorted_by_ops[0]['remaining_ops']}个)")
        
        # SPT: 按最短加工时间
        min_time_op = None
        min_time = float('inf')
        for op in ready_ops:
            for machine, time in op['machines']:
                if time < min_time:
                    min_time = time
                    min_time_op = (op['job'], op['op'], machine+1, time)
        print(f"SPT: 选择最短时间 -> 工件{min_time_op[0]}工序{min_time_op[1]}在机器{min_time_op[2]}(时长{min_time_op[3]})")
        
        # MWKR: 按剩余工作量
        sorted_by_work = sorted(ready_ops, key=lambda x: -x['remaining_work'])
        print(f"MWKR: 按剩余工作量排序 -> 工件{sorted_by_work[0]['job']}(剩余{sorted_by_work[0]['remaining_work']})")
        
    def analyze_algorithm_performance(self):
        """分析算法性能"""
        print("\n📈 算法性能分析")
        print("=" * 50)
        
        results, detailed_results = self.scheduler.run_all_rules()
        
        print("性能排名:")
        sorted_results = sorted(results.items(), key=lambda x: x[1])
        for i, (rule, makespan) in enumerate(sorted_results, 1):
            metrics = detailed_results[rule]['metrics']
            print(f"{i}. {rule}: Makespan={makespan}, 机器利用率={metrics['average_utilization']:.1%}")
        
        print(f"\n性能差异分析:")
        best = sorted_results[0][1]
        worst = sorted_results[-1][1]
        print(f"最佳vs最差: {worst-best} ({(worst/best-1)*100:.1f}%差异)")
        
        print(f"\n各规则特点:")
        print("FIFO: 简单公平，但可能导致长作业阻塞")
        print("MOPNR: 平衡工件进度，避免某些工件过早完成")
        print("SPT: 优先短作业，提高响应速度但可能饿死长作业")
        print("MWKR: 考虑总工作量，更全面的负载均衡")
        
    def validate_step_by_step(self):
        """逐步验证调度结果"""
        print("\n🔍 逐步验证调度结果")
        print("=" * 50)
        
        # 选择MOPNR的结果进行详细验证
        _, detailed_results = self.scheduler.run_all_rules()
        mopnr_schedule = detailed_results['MOPNR']['schedule']
        
        print("验证MOPNR调度结果的前5个操作:")
        
        for i, item in enumerate(mopnr_schedule[:5]):
            print(f"\n操作{i+1}: 工件{item['job']}工序{item['operation']}")
            print(f"  机器: {item['machine']}")
            print(f"  时间: {item['start_time']} -> {item['completion_time']} (时长{item['processing_time']})")
            
            # 验证加工时间
            job_id = item['job'] - 1
            op_id = item['operation'] - 1
            machine_id = item['machine'] - 1
            
            operation_machines = self.jobs[job_id][op_id]
            valid_time = None
            for m_id, p_time in operation_machines:
                if m_id == machine_id:
                    valid_time = p_time
                    break
            
            if valid_time == item['processing_time']:
                print(f"  ✅ 加工时间验证通过")
            else:
                print(f"  ❌ 加工时间错误: 期望{valid_time}, 实际{item['processing_time']}")
                
    def create_custom_test(self):
        """创建自定义测试案例"""
        print("\n🛠️ 创建自定义测试案例")
        print("=" * 50)
        
        # 创建一个3工件2机器的简单案例
        custom_jobs = [
            # 工件1: 2道工序
            [[(0, 2), (1, 3)], [(0, 1), (1, 4)]],
            # 工件2: 2道工序  
            [[(0, 3), (1, 1)], [(0, 2), (1, 2)]],
            # 工件3: 1道工序
            [[(0, 1), (1, 5)]]
        ]
        
        print("自定义案例:")
        for i, job in enumerate(custom_jobs):
            print(f"工件{i+1}: {len(job)}道工序")
            for j, op in enumerate(job):
                print(f"  工序{j+1}: {[(f'机器{m+1}', f'时长{t}') for m, t in op]}")
        
        # 手工计算最优解
        print(f"\n手工分析最优解:")
        print("工件1: 工序1(机器1,3) -> 工序2(机器1,1)")  
        print("工件2: 工序1(机器2,1) -> 工序2(机器1,2)")
        print("工件3: 工序1(机器1,1)")
        print("时间轴: 0-1:工件2工序1机器2, 0-3:工件1工序1机器1, 1-2:工件3工序1机器1, 3-4:工件1工序2机器1, 3-5:工件2工序2机器1")
        print("预期最优Makespan: 5")
        
        return custom_jobs
        
    def interactive_learning(self):
        """交互式学习主程序"""
        print("🎓 FJSP交互式学习导师")
        print("=" * 60)
        
        while True:
            print(f"\n请选择学习内容:")
            print("1. 数据结构详解")
            print("2. 调度规则对比")
            print("3. 算法性能分析") 
            print("4. 逐步验证结果")
            print("5. 自定义测试案例")
            print("6. 退出")
            
            try:
                choice = input("\n请输入选择 (1-6): ").strip()
                
                if choice == '1':
                    self.explain_data_structure()
                elif choice == '2':
                    self.compare_scheduling_rules()
                elif choice == '3':
                    self.analyze_algorithm_performance()
                elif choice == '4':
                    self.validate_step_by_step()
                elif choice == '5':
                    self.create_custom_test()
                elif choice == '6':
                    print("感谢使用FJSP学习导师！")
                    break
                else:
                    print("无效选择，请输入1-6")
                    
                input("\n按回车继续...")
                
            except KeyboardInterrupt:
                print("\n\n感谢使用FJSP学习导师！")
                break
            except Exception as e:
                print(f"发生错误: {e}")

if __name__ == "__main__":
    tutor = FJSPTutor()
    tutor.interactive_learning()
