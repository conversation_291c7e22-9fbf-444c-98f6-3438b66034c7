"""
FJSP代码验证工具集
帮助您逐步理解和验证调度算法的正确性
"""

def trace_scheduling_process(scheduler, jobs, rule_name):
    """
    跟踪调度过程，输出详细的决策步骤
    """
    print(f"\n=== {rule_name}调度过程跟踪 ===")
    
    # 模拟调度过程的关键步骤
    num_jobs = len(jobs)
    machine_available_time = [0] * scheduler.num_machines
    job_next_operation = [0] * num_jobs
    job_last_completion_time = [0] * num_jobs
    
    step = 1
    total_operations = sum(len(job) for job in jobs)
    completed_operations = 0
    
    while completed_operations < total_operations and step <= 10:  # 只显示前10步
        print(f"\n--- 步骤 {step} ---")
        
        # 显示当前状态
        print("机器可用时间:", machine_available_time)
        print("工件进度:", [f"工件{i+1}->工序{job_next_operation[i]+1}" 
                         for i in range(num_jobs) if job_next_operation[i] < len(jobs[i])])
        
        # 找到准备就绪的操作
        ready_operations = []
        for job_id in range(num_jobs):
            if job_next_operation[job_id] < len(jobs[job_id]):
                ready_time = job_last_completion_time[job_id]
                ready_operations.append((ready_time, job_id, job_next_operation[job_id]))
        
        if not ready_operations:
            break
            
        print("就绪操作:", [(f"工件{op[1]+1}工序{op[2]+1}", f"就绪时间{op[0]}") 
                         for op in ready_operations])
        
        # 根据规则选择操作（简化版FIFO）
        ready_operations.sort(key=lambda x: (x[0], x[1], x[2]))
        selected = ready_operations[0]
        _, selected_job, selected_op = selected
        
        # 选择机器
        operation_machines = jobs[selected_job][selected_op]
        print(f"工件{selected_job+1}工序{selected_op+1}的机器选项:", 
              [(f"机器{m+1}", f"时长{t}") for m, t in operation_machines])
        
        # 选择最早可用的机器
        best_machine = None
        best_start_time = float('inf')
        best_processing_time = 0
        
        for machine_id, processing_time in operation_machines:
            start_time = max(machine_available_time[machine_id], 
                           job_last_completion_time[selected_job])
            if start_time < best_start_time:
                best_start_time = start_time
                best_machine = machine_id
                best_processing_time = processing_time
        
        # 执行调度
        completion_time = best_start_time + best_processing_time
        machine_available_time[best_machine] = completion_time
        job_last_completion_time[selected_job] = completion_time
        job_next_operation[selected_job] += 1
        
        print(f"决策: 工件{selected_job+1}工序{selected_op+1} -> 机器{best_machine+1}")
        print(f"时间: 开始{best_start_time}, 时长{best_processing_time}, 完成{completion_time}")
        
        completed_operations += 1
        step += 1
    
    makespan = max(machine_available_time)
    print(f"\n{rule_name}最终Makespan: {makespan}")
    return makespan

def validate_constraints(schedule, jobs):
    """
    详细验证调度结果是否满足所有约束
    """
    print("\n=== 约束验证 ===")
    
    # 1. 工序优先级约束验证
    print("1. 检查工序优先级约束...")
    job_operations = {}
    precedence_errors = []
    
    for item in schedule:
        job_id = item['job'] - 1
        op_id = item['operation'] - 1
        start_time = item['start_time']
        completion_time = item['completion_time']
        
        if job_id not in job_operations:
            job_operations[job_id] = {}
        job_operations[job_id][op_id] = (start_time, completion_time)
        
        # 检查前序操作
        if op_id > 0:
            if op_id - 1 not in job_operations[job_id]:
                precedence_errors.append(f"工件{job_id+1}工序{op_id+1}的前序工序{op_id}未找到")
            else:
                prev_completion = job_operations[job_id][op_id - 1][1]
                if prev_completion > start_time:
                    precedence_errors.append(
                        f"工件{job_id+1}工序{op_id+1}开始时间{start_time} < 前序完成时间{prev_completion}")
    
    if precedence_errors:
        print("   ❌ 发现工序优先级约束违反:")
        for error in precedence_errors:
            print(f"      {error}")
    else:
        print("   ✅ 工序优先级约束满足")
    
    # 2. 机器冲突检验
    print("2. 检查机器冲突...")
    machine_schedules = {}
    conflict_errors = []
    
    for item in schedule:
        machine_id = item['machine']
        start_time = item['start_time']
        completion_time = item['completion_time']
        
        if machine_id not in machine_schedules:
            machine_schedules[machine_id] = []
        machine_schedules[machine_id].append((start_time, completion_time, item))
    
    for machine_id, operations in machine_schedules.items():
        operations.sort(key=lambda x: x[0])
        for i in range(len(operations) - 1):
            current_end = operations[i][1]
            next_start = operations[i + 1][0]
            if current_end > next_start:
                conflict_errors.append(
                    f"机器{machine_id}时间冲突: {operations[i][2]['job']}-{operations[i][2]['operation']}结束{current_end} > {operations[i+1][2]['job']}-{operations[i+1][2]['operation']}开始{next_start}")
    
    if conflict_errors:
        print("   ❌ 发现机器冲突:")
        for error in conflict_errors:
            print(f"      {error}")
    else:
        print("   ✅ 无机器冲突")
    
    # 3. 加工时间验证
    print("3. 检查加工时间正确性...")
    time_errors = []
    
    for item in schedule:
        job_id = item['job'] - 1
        op_id = item['operation'] - 1
        machine_id = item['machine'] - 1
        recorded_time = item['processing_time']
        actual_duration = item['completion_time'] - item['start_time']
        
        # 查找标准时间
        operation_machines = jobs[job_id][op_id]
        standard_time = None
        for m_id, p_time in operation_machines:
            if m_id == machine_id:
                standard_time = p_time
                break
        
        if standard_time is None:
            time_errors.append(f"工件{job_id+1}工序{op_id+1}不能在机器{machine_id+1}上加工")
        elif standard_time != recorded_time or actual_duration != recorded_time:
            time_errors.append(
                f"工件{job_id+1}工序{op_id+1}时间错误: 标准{standard_time}, 记录{recorded_time}, 实际{actual_duration}")
    
    if time_errors:
        print("   ❌ 发现加工时间错误:")
        for error in time_errors:
            print(f"      {error}")
    else:
        print("   ✅ 加工时间正确")
    
    return len(precedence_errors) == 0 and len(conflict_errors) == 0 and len(time_errors) == 0

def compare_with_known_benchmarks(results):
    """
    与已知基准对比结果
    """
    print("\n=== 基准对比 ===")
    mk01_optimal_range = (36, 42)
    mk01_good_range = (42, 50)
    mk01_acceptable_range = (50, 60)
    
    for rule_name, makespan in results.items():
        if makespan <= mk01_optimal_range[1]:
            status = "🏆 优秀"
        elif makespan <= mk01_good_range[1]:
            status = "✅ 良好"
        elif makespan <= mk01_acceptable_range[1]:
            status = "⚠️ 一般"
        else:
            status = "❌ 较差"
        
        print(f"{rule_name}: {makespan} {status}")
    
    print(f"\n参考范围:")
    print(f"最优解: {mk01_optimal_range[0]}-{mk01_optimal_range[1]}")
    print(f"良好解: {mk01_good_range[0]}-{mk01_good_range[1]}")
    print(f"可接受: {mk01_acceptable_range[0]}-{mk01_acceptable_range[1]}")

if __name__ == "__main__":
    print("这是FJSP验证工具集，请在主程序中调用相关函数")
