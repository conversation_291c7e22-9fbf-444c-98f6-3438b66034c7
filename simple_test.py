"""
简化的FJSP测试用例 - 用于手工验证算法正确性

这个简化版本只包含2个工件，每个工件2道工序，2台机器
可以手工计算最优解，然后对比算法结果
"""

class SimpleFJSPTest:
    def __init__(self):
        # 简化数据：2个工件，2道工序，2台机器
        self.jobs = [
            # 工件1：2道工序
            [
                [(0, 3), (1, 2)],  # 工序1：机器0用时3，机器1用时2
                [(0, 2), (1, 4)]   # 工序2：机器0用时2，机器1用时4
            ],
            # 工件2：2道工序  
            [
                [(0, 1), (1, 3)],  # 工序1：机器0用时1，机器1用时3
                [(0, 4), (1, 1)]   # 工序2：机器0用时4，机器1用时1
            ]
        ]
        
    def manual_optimal_solution(self):
        """
        手工计算最优解：
        
        最优调度方案：
        时间0-1: 工件2工序1 在机器0 (用时1)
        时间0-2: 工件1工序1 在机器1 (用时2) 
        时间1-5: 工件2工序2 在机器0 (用时4，但要等工序1完成)
        时间2-4: 工件1工序2 在机器0 (用时2，但要等机器0空闲和工序1完成)
        
        等等，让我重新计算...
        
        方案1：
        时间0-1: 工件2工序1 在机器0 (用时1)
        时间0-2: 工件1工序1 在机器1 (用时2)
        时间2-4: 工件1工序2 在机器0 (用时2)  
        时间1-2: 工件2工序2 在机器1 (用时1)
        
        Makespan = 4
        """
        print("=== 手工计算最优解 ===")
        print("工件1: 工序1(机器1,时长2) -> 工序2(机器0,时长2)")
        print("工件2: 工序1(机器0,时长1) -> 工序2(机器1,时长1)")
        print("时间轴:")
        print("0-1: 工件2工序1在机器0")
        print("0-2: 工件1工序1在机器1") 
        print("1-2: 工件2工序2在机器1")
        print("2-4: 工件1工序2在机器0")
        print("最优Makespan = 4")
        return 4
        
    def test_fifo(self):
        """测试FIFO规则"""
        print("\n=== FIFO规则手工模拟 ===")
        # FIFO按工件顺序：工件1工序1 -> 工件2工序1 -> 工件1工序2 -> 工件2工序2
        
        machine_time = [0, 0]  # 两台机器的可用时间
        job_completion = [0, 0]  # 两个工件的完成时间
        
        # 工件1工序1：选择最早可用机器（机器1用时2更短）
        start_time = max(machine_time[1], job_completion[0])  # max(0, 0) = 0
        machine_time[1] = start_time + 2  # 机器1在时间2可用
        job_completion[0] = start_time + 2  # 工件1在时间2完成工序1
        print(f"工件1工序1: 机器1, 开始{start_time}, 完成{job_completion[0]}")
        
        # 工件2工序1：选择最早可用机器（机器0用时1更短）
        start_time = max(machine_time[0], job_completion[1])  # max(0, 0) = 0
        machine_time[0] = start_time + 1  # 机器0在时间1可用
        job_completion[1] = start_time + 1  # 工件2在时间1完成工序1
        print(f"工件2工序1: 机器0, 开始{start_time}, 完成{job_completion[1]}")
        
        # 工件1工序2：需要等工序1完成，选择最早可用机器
        start_time = max(machine_time[0], job_completion[0])  # max(1, 2) = 2
        machine_time[0] = start_time + 2  # 机器0在时间4可用
        job_completion[0] = start_time + 2  # 工件1在时间4完成
        print(f"工件1工序2: 机器0, 开始{start_time}, 完成{job_completion[0]}")
        
        # 工件2工序2：需要等工序1完成，选择最早可用机器
        start_time = max(machine_time[1], job_completion[1])  # max(2, 1) = 2
        machine_time[1] = start_time + 1  # 机器1在时间3可用
        job_completion[1] = start_time + 1  # 工件2在时间3完成
        print(f"工件2工序2: 机器1, 开始{start_time}, 完成{job_completion[1]}")
        
        makespan = max(machine_time)
        print(f"FIFO Makespan = {makespan}")
        return makespan

if __name__ == "__main__":
    test = SimpleFJSPTest()
    optimal = test.manual_optimal_solution()
    fifo_result = test.test_fifo()
    
    print(f"\n=== 结果对比 ===")
    print(f"手工最优解: {optimal}")
    print(f"FIFO结果: {fifo_result}")
    print(f"FIFO效果: {'良好' if fifo_result <= optimal * 1.2 else '一般'}")
