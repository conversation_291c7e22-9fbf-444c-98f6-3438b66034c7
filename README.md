# 柔性作业车间调度问题演示

## 概述

本项目演示了柔性作业车间调度问题(FJSP)的四种经典调度规则，包含简化实例和标准MK01基准测试。

## 四种调度规则

### 1. FIFO (First In First Out) - 先到先服务
- **原理**：按操作就绪时间先后顺序调度
- **优点**：公平性好，实现简单
- **适用场景**：公平性要求高的环境

### 2. MOPNR (Most Operations Remaining) - 剩余操作数最多优先
- **原理**：优先调度剩余操作数最多的工件
- **优点**：平衡各工件进度，避免某些工件过早完成
- **适用场景**：工件操作数差异大的场景

### 3. SPT (Shortest Processing Time) - 最短加工时间优先
- **原理**：选择当前所有可用操作-机器组合中加工时间最短的
- **优点**：快速完成短任务，提高系统响应速度
- **适用场景**：响应时间敏感的场景

### 4. MWKR (Most Work Remaining) - 剩余工作量最多优先
- **原理**：优先调度剩余工作量最大的工件
- **优点**：考虑工作量分布，平衡系统负载
- **适用场景**：工件加工时间差异大的场景

## 项目文件说明

### 核心文件
- `test.py` - MK01标准基准测试，包含四种调度规则的完整实现
- `mk01_simple_gantt.py` - MK01甘特图生成脚本
- `fjsp_example_demo.py` - 简化实例演示（3工件3机器）
- `run_demo_text_only.py` - 纯文本版演示（解决中文显示问题）

### 生成文件
- `mk01_gantt_comparison.png` - MK01四种规则甘特图对比
- `mk01_mopnr_schedule.txt` - 最佳规则详细调度序列

## 运行方式

### 1. 简化实例演示
```bash
python run_demo_text_only.py
```
运行3工件3机器的简化实例，展示调度过程。

### 2. MK01标准基准测试
```bash
python test.py
```
运行MK01标准实例（10工件6机器），输出四种规则的性能对比。

### 3. 生成MK01甘特图
```bash
python mk01_simple_gantt.py
```
生成MK01四种调度规则的甘特图对比。

## MK01测试结果

| 规则 | 总完工时间 | 与最优解差距 | 性能排名 |
|------|------------|--------------|----------|
| MOPNR | 42 | 0 | 🥇 |
| FIFO | 47 | +5 | 🥈 |
| MWKR | 50 | +8 | 🥉 |
| SPT | 86 | +44 | 4️⃣ |

**说明**：MK01已知最优解范围为36-42，MOPNR规则达到了最优解上限。

## 技术特点

- ✅ 完整的约束验证（工序优先级、机器冲突检查）
- ✅ 详细的性能指标计算（makespan、机器利用率）
- ✅ 中文字体支持的甘特图可视化
- ✅ 标准MK01基准测试验证
- ✅ 详细的调度过程输出和分析

## 算法实现思路详细说明

### 1.1 FIFO (First In First Out) - 先到先服务调度规则

**核心思想：**

FIFO规则遵循"先到先服务"的公平原则，优先调度最早准备就绪的操作。

**算法步骤：**

1.**初始化**：设置机器可用时间数组、工件进度跟踪数组

2.**循环调度**：

- 识别所有准备就绪的操作（前序操作已完成）
- 按就绪时间排序，选择最早就绪的操作
- 为选中操作分配最早可用的兼容机器
- 更新机器可用时间和工件进度

3.**结果计算**：返回makespan和详细调度方案

**优先级判断标准：**

- 主要标准：操作就绪时间（越早优先级越高）
- 次要标准：工件ID和操作ID（用于稳定排序）

**适用场景：**

- 公平性要求高的环境
- 作为基准算法进行对比
- 简单易实现的调度策略

**性能特点：**

- 实现简单，计算复杂度低
- 调度结果稳定可预测
- 性能中等，适合作为基准

### 1.2 MOPNR (Most Operations Remaining) - 剩余操作数最多优先

**核心思想：**

优先调度剩余操作数最多的工件，目的是平衡各工件的进度，避免某些工件过早完成而其他工件滞后。

**剩余操作数计算公式：**

```

剩余操作数 = 工件总操作数 - 当前操作索引 - 1

```

**算法特点：**

-**负载均衡**：避免工件间进度差异过大

-**全局视角**：考虑工件的整体完成情况

-**适应性强**：对不同工件操作数差异较大的情况效果好

**排序规则：**

1. 剩余操作数降序（剩余操作多的优先）
2. 就绪时间升序（时间早的优先）
3. 工件ID和操作ID升序（稳定性保证）

**性能特点：**

- 在MK01测试中表现最佳（makespan=42）
- 机器利用率高（77.38%）
- 适合操作数差异较大的问题实例

### 1.3 SPT (Shortest Processing Time) - 最短加工时间优先

**核心思想：**

采用贪心策略，总是选择当前可用的操作-机器组合中加工时间最短的组合。

**与其他规则的关键区别：**

-**FIFO/MOPNR/MWKR**：先选操作，再选机器

-**SPT**：同时考虑操作和机器，选择最优组合

**算法步骤：**

1. 枚举所有准备就绪操作的所有兼容机器组合
2. 计算每个操作-机器对的开始时间和加工时间
3. 选择加工时间最短的组合
4. 执行调度并更新状态

**优缺点分析：**

-**优点**：快速完成短任务，提高系统吞吐量

-**缺点**：容易导致长任务延迟，整体makespan可能较大

-**适用场景**：任务长度差异很大，优先完成短任务的场景

**性能特点：**

- 在MK01测试中表现较差（makespan=86）
- 机器利用率低（29.65%）
- 典型的局部最优策略

### 1.4 MWKR (Most Work Remaining) - 剩余工作量最多优先

**核心思想：**

优先调度剩余工作量最大的工件，通过优先处理"重"工件来平衡系统负载。

**剩余工作量计算方法：**

```

剩余工作量 = Σ(从当前操作到最后操作的平均加工时间)

平均加工时间 = Σ(该操作所有机器的加工时间) / 机器数量

```

**算法改进：**

-**原始错误**：只计算当前操作之后的工作量

-**修正后**：包含当前操作在内的所有剩余工作量

-**影响**：makespan从52改善到50

**适用场景：**

- 工件加工时间差异较大
- 需要避免长任务在后期造成瓶颈
- 负载均衡要求较高的环境

**性能特点：**

- 修正后性能良好（makespan=50）
- 机器利用率适中（67.67%）
- 考虑了任务的"重量"而非仅仅数量

## 2. 数据结构设计思路

### 2.1 核心数据结构

```python

# 工件数据结构

jobs[i][j]=[(machine_id, processing_time),...]

# jobs[i][j] 表示工件i的第j个操作可以在哪些机器上加工及对应时间


# 状态跟踪数组

machine_available_time =[0]* num_machines    # 每台机器的最早可用时间

job_next_operation =[0]* num_jobs            # 每个工件下一个要处理的操作索引

job_last_completion_time =[0]* num_jobs      # 每个工件上一个操作的完成时间

```

### 2.2 调度循环执行流程

```python

while completed_operations < total_operations:

    # 1. 识别准备就绪的操作

    ready_operations = find_ready_operations()

  

    # 2. 根据规则选择操作（和机器）

    selected_operation = select_by_rule(ready_operations)

  

    # 3. 执行调度

    schedule_operation(selected_operation)

  

    # 4. 更新状态

    update_system_state()

```

## 3. 约束处理机制

### 3.1 工序优先级约束

-**实现方式**：通过 `job_last_completion_time`确保前序操作完成

-**检查方法**：操作开始时间 ≥ 前序操作完成时间

### 3.2 机器容量约束

-**实现方式**：通过 `machine_available_time`跟踪机器占用情况

-**检查方法**：操作开始时间 ≥ 机器可用时间

### 3.3 开始时间计算

```python

start_time =max(machine_available_time[machine_id], job_ready_time)

```

##4. 性能对比分析

| 规则 | Makespan | 机器利用率 | 特点 | 适用场景 |

|------|----------|------------|------|----------|

| MOPNR | 42 | 77.38% | 最佳性能，负载均衡 | 操作数差异大 |

| FIFO | 47 | 67.73% | 稳定可预测 | 公平性要求高 |

| MWKR | 50 | 67.67% | 考虑任务重量 | 加工时间差异大 |

| SPT | 86 | 29.65% | 局部最优 | 优先短任务 |

## 5. 验证方法总结

### 5.1 约束验证

- ✅ 工序优先级约束检查
- ✅ 机器冲突检查
- ✅ 加工时间正确性验证

### 5.2 性能指标

- ✅ Makespan计算
- ✅ 机器利用率统计
- ✅ 与基准结果对比

### 5.3 测试用例

- ✅ 简单测试用例手工验证
- ✅ MK01标准基准测试
- ✅ 基准范围对比验证

所有四种调度规则均通过了完整的验证测试，代码实现正确可靠。
