"""
FJSP学习和验证主程序
帮助您系统性地理解和验证柔性作业车间调度问题的代码实现
"""

from test import FJSPScheduler
from verification_tools import trace_scheduling_process, validate_constraints, compare_with_known_benchmarks

def learning_workflow():
    """
    完整的学习和验证工作流程
    """
    print("🎯 柔性作业车间调度问题(FJSP)学习验证流程")
    print("=" * 60)
    
    # 第一步：理解问题和数据
    print("\n📚 第一步：理解问题和数据结构")
    scheduler = FJSPScheduler()
    jobs = scheduler.parse_mk01_data()
    
    print(f"MK01实例基本信息:")
    print(f"- 工件数量: {len(jobs)}")
    print(f"- 机器数量: {scheduler.num_machines}")
    print(f"- 总操作数: {sum(len(job) for job in jobs)}")
    
    # 显示第一个工件的详细信息作为示例
    print(f"\n工件1详细信息（共{len(jobs[0])}道工序）:")
    for i, operation in enumerate(jobs[0]):
        print(f"  工序{i+1}: {[(f'机器{m+1}', f'时长{t}') for m, t in operation]}")
    
    input("\n按回车继续到第二步...")
    
    # 第二步：跟踪调度过程
    print("\n🔍 第二步：跟踪调度决策过程")
    print("我们将跟踪FIFO规则的前几个调度决策，帮助您理解算法逻辑")
    
    # 使用简化数据进行跟踪
    simple_jobs = [
        [[(0, 3), (1, 2)], [(0, 2), (1, 4)]],  # 工件1
        [[(0, 1), (1, 3)], [(0, 4), (1, 1)]]   # 工件2
    ]
    
    simple_scheduler = FJSPScheduler()
    simple_scheduler.num_machines = 2
    trace_scheduling_process(simple_scheduler, simple_jobs, "FIFO")
    
    input("\n按回车继续到第三步...")
    
    # 第三步：运行完整算法并验证
    print("\n⚙️ 第三步：运行完整算法并验证结果")
    
    # 运行所有规则
    results, detailed_results = scheduler.run_all_rules()
    
    # 验证每个规则的结果
    print("\n🔍 详细验证每个规则的调度结果:")
    for rule_name, details in detailed_results.items():
        print(f"\n--- {rule_name}规则验证 ---")
        schedule = details['schedule']
        validation = details['validation']
        
        # 显示验证结果
        if validation['precedence_valid'] and not validation['machine_conflict'] and validation['processing_time_valid']:
            print("✅ 所有约束都满足")
        else:
            print("❌ 发现约束违反:")
            if not validation['precedence_valid']:
                print("  - 工序优先级约束违反")
            if validation['machine_conflict']:
                print("  - 机器冲突")
            if not validation['processing_time_valid']:
                print("  - 加工时间错误")
        
        # 显示性能指标
        metrics = details['metrics']
        print(f"性能指标:")
        print(f"  - Makespan: {metrics['makespan']}")
        print(f"  - 平均机器利用率: {metrics['average_utilization']:.2%}")
        print(f"  - 平均完成时间: {metrics['average_completion_time']:.1f}")
    
    input("\n按回车继续到第四步...")
    
    # 第四步：结果分析和对比
    print("\n📊 第四步：结果分析和基准对比")
    compare_with_known_benchmarks(results)
    
    # 分析不同规则的特点
    print(f"\n🎯 算法特点分析:")
    sorted_results = sorted(results.items(), key=lambda x: x[1])
    
    print(f"最佳规则: {sorted_results[0][0]} (Makespan: {sorted_results[0][1]})")
    print(f"最差规则: {sorted_results[-1][0]} (Makespan: {sorted_results[-1][1]})")
    print(f"性能差异: {sorted_results[-1][1] - sorted_results[0][1]} ({(sorted_results[-1][1]/sorted_results[0][1]-1)*100:.1f}%)")
    
    # 第五步：学习建议
    print(f"\n💡 第五步：学习建议和下一步")
    print("基于您的验证结果，建议：")
    
    if all(details['validation']['precedence_valid'] and 
           not details['validation']['machine_conflict'] and 
           details['validation']['processing_time_valid'] 
           for details in detailed_results.values()):
        print("✅ 代码实现正确，所有约束都满足")
    else:
        print("⚠️ 发现实现问题，需要检查约束验证部分")
    
    best_makespan = min(results.values())
    if best_makespan <= 42:
        print("🏆 算法性能优秀，达到了已知最优解范围")
    elif best_makespan <= 50:
        print("✅ 算法性能良好，在可接受范围内")
    else:
        print("⚠️ 算法性能一般，可以考虑改进")
    
    print(f"\n📖 进一步学习建议:")
    print("1. 尝试修改调度规则的优先级策略")
    print("2. 实现更高级的元启发式算法（如遗传算法、模拟退火）")
    print("3. 测试其他标准基准实例（如MK02-MK10）")
    print("4. 分析机器利用率和负载均衡")
    print("5. 可视化调度甘特图")

def quick_verification():
    """
    快速验证代码正确性的简化流程
    """
    print("🚀 快速验证模式")
    print("=" * 30)
    
    scheduler = FJSPScheduler()
    results, detailed_results = scheduler.run_all_rules()
    
    # 检查基本正确性
    all_valid = True
    for rule_name, details in detailed_results.items():
        validation = details['validation']
        if not (validation['precedence_valid'] and 
                not validation['machine_conflict'] and 
                validation['processing_time_valid']):
            all_valid = False
            print(f"❌ {rule_name}规则验证失败")
    
    if all_valid:
        print("✅ 所有规则都通过约束验证")
    
    # 检查性能合理性
    best_makespan = min(results.values())
    if best_makespan <= 60:
        print(f"✅ 最佳性能{best_makespan}在合理范围内")
    else:
        print(f"⚠️ 最佳性能{best_makespan}可能存在问题")
    
    print(f"\n结果汇总: {results}")
    return all_valid and best_makespan <= 60

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        # 快速验证模式
        success = quick_verification()
        print(f"\n验证结果: {'通过' if success else '失败'}")
    else:
        # 完整学习模式
        learning_workflow()
        print(f"\n🎉 学习验证流程完成！")
        print("您现在应该能够理解和验证FJSP代码的正确性了。")
